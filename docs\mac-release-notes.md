# MixVideo Mac 客户端发布说明

## 版本 0.2.1 - Mac 客户端支持

### 🍎 新增 Mac 客户端支持

我们很高兴地宣布 MixVideo 现在正式支持 macOS 平台！

#### 支持的 Mac 版本
- **Universal 构建**: 同时支持 Intel 和 Apple Silicon Mac
- **Intel 专用构建**: 针对 Intel Mac 优化
- **Apple Silicon 专用构建**: 针对 M1/M2/M3 Mac 优化

#### 系统要求
- macOS 10.15 (Catalina) 或更高版本
- 至少 4GB 可用磁盘空间
- 推荐 8GB 或更多内存

### 🚀 Mac 特有功能

#### 原生 macOS 体验
- 完全原生的 macOS 应用程序
- 支持 macOS 深色/浅色主题自动切换
- 原生文件对话框和系统集成
- 支持 macOS 手势和快捷键

#### 性能优化
- 针对 Apple Silicon 优化的性能
- 更快的视频处理和 AI 分析
- 低内存占用和高效的资源管理

#### 安全性
- 代码签名和公证支持
- 沙盒安全模型
- 符合 macOS 安全标准

### 📦 安装方式

#### 方式一：DMG 安装包（推荐）
1. 下载对应您 Mac 型号的 DMG 文件
2. 双击 DMG 文件打开
3. 将 MixVideo Desktop 拖拽到应用程序文件夹
4. 从启动台或应用程序文件夹启动应用

#### 方式二：从源码构建
```bash
# 克隆项目
git clone ssh://**********************:222/imeepos/mixvideo-v2.git
cd mixvideo-v2

# 自动设置环境
bash scripts/setup-mac.sh

# 构建应用
bash scripts/build-mac.sh
```

### 🔧 开发者信息

#### 构建目标
- `universal-apple-darwin`: Universal 二进制文件
- `x86_64-apple-darwin`: Intel Mac
- `aarch64-apple-darwin`: Apple Silicon Mac

#### 构建命令
```bash
# Universal 构建
pnpm tauri:build:mac

# Intel 构建
pnpm tauri:build:mac:intel

# Apple Silicon 构建
pnpm tauri:build:mac:arm
```

### 🐛 已知问题

#### 首次启动
- 首次启动可能需要在系统偏好设置中允许应用运行
- 某些 macOS 版本可能显示"无法验证开发者"警告

#### 解决方案
1. 右键点击应用程序，选择"打开"
2. 或在系统偏好设置 > 安全性与隐私中允许应用运行

#### 性能相关
- 在某些较老的 Intel Mac 上，视频处理可能较慢
- 建议在处理大型视频文件时关闭其他应用程序

### 🔄 从其他平台迁移

#### 数据兼容性
- 项目文件完全兼容 Windows 和 Linux 版本
- 数据库格式统一，可直接迁移
- 配置文件自动适配 macOS 路径

#### 迁移步骤
1. 从旧平台导出项目数据
2. 在 Mac 上安装 MixVideo
3. 导入项目数据
4. 验证功能正常

### 🆕 Mac 专有改进

#### UI/UX 优化
- 适配 macOS 设计语言
- 支持 Retina 显示屏高分辨率
- 优化触控板手势支持

#### 文件系统集成
- 支持 macOS 文件标签
- 集成 Spotlight 搜索
- 支持快速预览 (Quick Look)

### 📋 更新计划

#### 即将推出
- App Store 版本
- 自动更新功能
- 更多 macOS 特有功能

#### 长期规划
- iCloud 同步支持
- Shortcuts 应用集成
- 更深度的系统集成

### 🙏 致谢

感谢所有 Beta 测试用户的反馈和建议，特别是：
- macOS 兼容性测试
- 性能优化建议
- UI/UX 改进意见

### 📞 支持

如果您在使用 Mac 版本时遇到问题：

1. 查看 [Mac 设置指南](mac-setup.md)
2. 检查 [故障排除文档](mac-setup.md#故障排除)
3. 在 GitHub 上提交 Issue
4. 联系技术支持

### 🔗 相关链接

- [Mac 设置指南](mac-setup.md)
- [开发者文档](../README.md)
- [API 文档](api/)
- [更新日志](../CHANGELOG.md)

---

**下载链接:**
- [Universal (Intel + Apple Silicon)](releases/latest)
- [Intel 专用版本](releases/latest)
- [Apple Silicon 专用版本](releases/latest)

**校验和:**
- Universal: `SHA256: [将在发布时更新]`
- Intel: `SHA256: [将在发布时更新]`
- Apple Silicon: `SHA256: [将在发布时更新]`
