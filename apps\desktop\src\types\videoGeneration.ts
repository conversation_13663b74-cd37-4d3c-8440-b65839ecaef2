// 视频生成相关类型定义

export interface VideoGenerationTask {
  id: string;
  model_id: string;
  prompt_config: VideoPromptConfig;
  selected_photos: string[];
  status: VideoGenerationStatus;
  result?: VideoGenerationResult;
  error_message?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

export interface VideoPromptConfig {
  product: string;      // 产品描述
  scene: string;        // 场景描述
  model_desc: string;   // 模特描述
  template: string;     // 模板类型
  duplicate: number;    // 生成数量
}

export enum VideoGenerationStatus {
  Pending = "Pending",
  Processing = "Processing", 
  Completed = "Completed",
  Failed = "Failed",
  Cancelled = "Cancelled"
}

export interface VideoGenerationResult {
  video_urls: string[];
  video_paths: string[];
  generation_time: number;
  api_response?: string;
}

export interface CreateVideoGenerationRequest {
  model_id: string;
  prompt_config: VideoPromptConfig;
  selected_photos: string[];
}

export interface VideoGenerationQueryParams {
  model_id?: string;
  status?: VideoGenerationStatus;
  limit?: number;
  offset?: number;
}

export interface DifyApiConfig {
  host: string;
  api_key: string;
}

export interface DifyApiRequest {
  inputs: DifyInputs;
  response_mode: string;
  user: string;
}

export interface DifyInputs {
  product: string;
  scene: string;
  model: string;
  image: string;
  duplicate: number;
  template: string;
}

export interface DifyApiResponse {
  data?: DifyResponseData;
  error?: string;
}

export interface DifyResponseData {
  outputs: DifyOutputs;
}

export interface DifyOutputs {
  output: string[];
}

// 视频生成表单数据
export interface VideoGenerationFormData {
  product: string;
  scene: string;
  model_desc: string;
  template: string;
  duplicate: number;
  selected_photos: string[];
}

// 视频生成表单错误
export interface VideoGenerationFormErrors {
  product?: string;
  scene?: string;
  model_desc?: string;
  template?: string;
  duplicate?: string;
  selected_photos?: string;
}

// 预设模板选项
export interface TemplateOption {
  value: string;
  label: string;
  description?: string;
}

// 常用模板预设
export const TEMPLATE_OPTIONS: TemplateOption[] = [
  { value: "抚媚眼神", label: "抚媚眼神", description: "展现模特魅惑的眼神和表情" },
  { value: "清纯甜美", label: "清纯甜美", description: "展现模特清纯可爱的一面" },
  { value: "性感妩媚", label: "性感妩媚", description: "展现模特性感迷人的魅力" },
  { value: "优雅知性", label: "优雅知性", description: "展现模特优雅大方的气质" },
  { value: "活力青春", label: "活力青春", description: "展现模特青春活力的状态" },
  { value: "神秘冷艳", label: "神秘冷艳", description: "展现模特神秘高冷的气质" },
];

// 常用场景预设
export const SCENE_OPTIONS = [
  "室内可爱简约的女性卧室",
  "现代简约的客厅环境",
  "温馨浪漫的咖啡厅",
  "时尚现代的摄影棚",
  "自然清新的户外花园",
  "优雅精致的酒店套房",
  "艺术感十足的工作室",
  "温暖舒适的家居环境",
];

// 常用产品描述预设
export const PRODUCT_OPTIONS = [
  "超短牛仔裙（白色紧身蕾丝短袖）",
  "黑色修身连衣裙",
  "白色衬衫配黑色短裙",
  "粉色针织毛衣配牛仔裤",
  "红色晚礼服",
  "休闲运动装",
  "职业套装",
  "夏日清新连衣裙",
];

// 视频生成API相关类型
export interface VideoGenerationAPI {
  createVideoGenerationTask: (request: CreateVideoGenerationRequest) => Promise<VideoGenerationTask>;
  getVideoGenerationTask: (taskId: string) => Promise<VideoGenerationTask | null>;
  getVideoGenerationTasks: (params?: VideoGenerationQueryParams) => Promise<VideoGenerationTask[]>;
  cancelVideoGenerationTask: (taskId: string) => Promise<void>;
  deleteVideoGenerationTask: (taskId: string) => Promise<void>;
  retryVideoGenerationTask: (taskId: string) => Promise<VideoGenerationTask>;
}

// 视频生成状态显示配置
export const VIDEO_GENERATION_STATUS_CONFIG = {
  [VideoGenerationStatus.Pending]: {
    label: "等待中",
    color: "text-yellow-600",
    bgColor: "bg-yellow-50",
    borderColor: "border-yellow-200",
  },
  [VideoGenerationStatus.Processing]: {
    label: "处理中",
    color: "text-blue-600", 
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200",
  },
  [VideoGenerationStatus.Completed]: {
    label: "已完成",
    color: "text-green-600",
    bgColor: "bg-green-50", 
    borderColor: "border-green-200",
  },
  [VideoGenerationStatus.Failed]: {
    label: "失败",
    color: "text-red-600",
    bgColor: "bg-red-50",
    borderColor: "border-red-200", 
  },
  [VideoGenerationStatus.Cancelled]: {
    label: "已取消",
    color: "text-gray-600",
    bgColor: "bg-gray-50",
    borderColor: "border-gray-200",
  },
};
