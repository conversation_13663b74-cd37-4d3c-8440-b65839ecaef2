#!/bin/bash

# MixVideo Mac 开发环境设置脚本
# 用于在 macOS 上设置 MixVideo 开发环境

set -e

echo "🍎 设置 MixVideo Mac 开发环境..."

# 检查是否在 macOS 系统上运行
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ 错误: 此脚本只能在 macOS 系统上运行"
    exit 1
fi

# 检查并安装 Homebrew
if ! command -v brew &> /dev/null; then
    echo "🍺 安装 Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
else
    echo "✅ Homebrew 已安装"
fi

# 检查并安装 Node.js
if ! command -v node &> /dev/null; then
    echo "📦 安装 Node.js..."
    brew install node
else
    echo "✅ Node.js 已安装: $(node --version)"
fi

# 检查并安装 pnpm
if ! command -v pnpm &> /dev/null; then
    echo "📦 安装 pnpm..."
    npm install -g pnpm
else
    echo "✅ pnpm 已安装: $(pnpm --version)"
fi

# 检查并安装 Rust
if ! command -v rustc &> /dev/null; then
    echo "🦀 安装 Rust..."
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source ~/.cargo/env
else
    echo "✅ Rust 已安装: $(rustc --version)"
fi

# 更新 Rust
echo "🔄 更新 Rust..."
rustup update

# 添加 macOS 构建目标
echo "🎯 添加 macOS 构建目标..."
rustup target add x86_64-apple-darwin
rustup target add aarch64-apple-darwin

# 安装 Tauri CLI
echo "🔧 安装 Tauri CLI..."
pnpm add -g @tauri-apps/cli

# 安装项目依赖
echo "📦 安装项目依赖..."
pnpm install

# 安装桌面应用依赖
echo "📱 安装桌面应用依赖..."
cd apps/desktop
pnpm install
cd ../..

# 检查 Xcode Command Line Tools
if ! xcode-select -p &> /dev/null; then
    echo "🔨 安装 Xcode Command Line Tools..."
    xcode-select --install
    echo "⚠️  请在弹出的对话框中完成 Xcode Command Line Tools 的安装，然后重新运行此脚本"
    exit 1
else
    echo "✅ Xcode Command Line Tools 已安装"
fi

# 验证环境
echo ""
echo "🔍 验证开发环境..."
echo "Node.js: $(node --version)"
echo "pnpm: $(pnpm --version)"
echo "Rust: $(rustc --version)"
echo "Cargo: $(cargo --version)"
echo "Tauri CLI: $(tauri --version 2>/dev/null || echo '未安装')"

echo ""
echo "✅ Mac 开发环境设置完成!"
echo ""
echo "🚀 现在您可以运行以下命令:"
echo "   开发模式: pnpm tauri:dev"
echo "   构建应用: pnpm tauri:build:mac"
echo "   或使用构建脚本: bash scripts/build-mac.sh"
echo ""
echo "📚 更多信息请查看 README.md"
