# MixVideo AI视频创作平台

<div align="center">

![MixVideo Logo](https://img.shields.io/badge/MixVideo-AI视频创作平台-blue?style=for-the-badge)

**构建全球领先的AI驱动视频创作生态平台，让每个人都能轻松创作出专业级的视频内容**

[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Version](https://img.shields.io/badge/version-0.1.25-blue.svg)](CHANGELOG.md)
[![Tauri](https://img.shields.io/badge/Tauri-2.0-orange.svg)](https://tauri.app/)
[![React](https://img.shields.io/badge/React-18.0-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)](https://www.typescriptlang.org/)

</div>

## 🎯 产品愿景

### 愿景声明
**"构建全球领先的AI驱动视频创作生态平台，让每个人都能轻松创作出专业级的视频内容"**

### 使命
通过AI技术和创作者生态，降低视频创作门槛，提升创作效率，让优质内容创作变得触手可及，同时为创作者提供可持续的收入来源。

## ✨ 核心特性

### 🤖 AI智能分析
- **智能视频分类**：基于Gemini AI的视频内容自动分类
- **场景检测**：自动识别视频场景变化，智能分割片段
- **质量评估**：AI评估视频质量，提供优化建议

### 📁 项目管理
- **项目组织**：支持多项目管理，本地路径绑定
- **素材管理**：视频、音频、图片素材统一管理
- **版本控制**：素材版本追踪，支持回滚操作

### 🎬 模板系统
- **模板创建**：可视化模板编辑器
- **智能匹配**：AI驱动的素材与模板自动匹配
- **批量处理**：一键应用模板到多个素材

### 👥 模特管理
- **模特档案**：完整的模特信息管理系统
- **照片管理**：支持多类型照片分类存储
- **关联匹配**：素材与模特自动关联

## 🚀 快速开始

### 环境要求

- **Node.js** >= 18.0.0
- **pnpm** >= 8.0.0
- **Rust** >= 1.70.0
- **Tauri CLI** >= 2.0.0

#### 平台特定要求

**Windows:**
- Visual Studio Build Tools 或 Visual Studio Community
- Windows 10 SDK

**macOS:**
- Xcode Command Line Tools
- macOS 10.15 (Catalina) 或更高版本

**Linux:**
- 参考 [Tauri 官方文档](https://tauri.app/v1/guides/getting-started/prerequisites#setting-up-linux)

### 安装步骤

#### 通用安装

1. **克隆项目**
```bash
git clone ssh://**********************:222/imeepos/mixvideo-v2.git
cd mixvideo-v2
```

2. **安装依赖**
```bash
pnpm install
```

3. **启动开发环境**
```bash
pnpm tauri:dev
```

4. **构建生产版本**
```bash
pnpm tauri:build
```

#### macOS 快速设置

对于 macOS 用户，我们提供了自动化设置脚本：

```bash
# 自动设置开发环境
bash scripts/setup-mac.sh

# 构建 Mac 应用
bash scripts/build-mac.sh
```

**Mac 构建选项:**
- `pnpm tauri:build:mac` - Universal 构建 (Intel + Apple Silicon)
- `pnpm tauri:build:mac:intel` - Intel 专用构建
- `pnpm tauri:build:mac:arm` - Apple Silicon 专用构建

详细的 Mac 设置指南请参考 [Mac 设置文档](docs/mac-setup.md)。

## 🏗️ 技术架构

### 前端技术栈
- **框架**：React 18 + TypeScript
- **状态管理**：Zustand
- **UI组件**：TailwindCSS + Lucide Icons
- **路由**：React Router
- **构建工具**：Vite

### 后端技术栈
- **框架**：Tauri 2.0 (Rust)
- **数据库**：SQLite
- **AI集成**：Google Gemini API
- **文件处理**：FFmpeg

### 项目结构
```
mixvideo/
├── apps/
│   └── desktop/           # Tauri桌面应用
│       ├── src/           # React前端代码
│       │   ├── components/    # UI组件
│       │   ├── pages/         # 页面组件
│       │   ├── store/         # 状态管理
│       │   ├── services/      # 业务服务
│       │   └── types/         # 类型定义
│       └── src-tauri/     # Rust后端代码
│           ├── src/
│           │   ├── business/      # 业务逻辑层
│           │   ├── data/          # 数据访问层
│           │   └── presentation/  # 表现层
│           └── Cargo.toml
├── packages/              # 共享包
├── docs/                  # 文档
└── promptx/              # 开发规范
```

## 📖 功能详解

### 🎥 视频处理流程

1. **素材导入**
   - 支持批量导入视频、音频、图片
   - 自动提取元数据信息
   - 生成缩略图预览

2. **智能分析**
   - AI场景检测和分割
   - 内容分类和标签
   - 质量评估和优化建议

3. **模板匹配**
   - 基于AI的智能素材匹配
   - 支持自定义匹配规则
   - 批量处理和预览

4. **输出导出**
   - 多格式输出支持
   - 自定义参数配置
   - 批量导出处理

### 🔧 AI分类设置

- **分类管理**：创建和管理AI分类类别
- **提示词配置**：自定义AI分类提示词
- **实时预览**：提示词效果实时预览
- **批量分类**：一键对项目素材进行AI分类

### 📊 数据统计

- **项目概览**：素材数量、分类统计
- **使用情况**：素材使用率分析
- **性能监控**：AI分类任务进度
- **质量报告**：内容质量评估报告

## 🛠️ 开发指南

### 代码规范

本项目遵循严格的开发规范，详见 `promptx/` 目录：

- **Tauri开发规范**：`promptx/tauri-desktop-app-expert/`
- **前端开发规范**：`promptx/frontend-developer/`
- **UI/UX设计规范**：遵循现代化设计原则

### 开发流程

1. **创建功能分支**
```bash
git checkout -b feature/your-feature-name
```

2. **开发和测试**
```bash
pnpm tauri:dev  # 开发模式
pnpm test       # 运行测试
```

3. **提交代码**
```bash
git add .
git commit -m "feat: 添加新功能描述"
```

4. **合并到主分支**
```bash
git checkout master
git merge feature/your-feature-name
git push origin master
```

### 测试策略

- **单元测试**：核心业务逻辑测试
- **集成测试**：API和数据库集成测试
- **E2E测试**：用户界面端到端测试
- **性能测试**：AI处理性能基准测试

## 🔌 API集成

### Gemini AI集成

```typescript
// AI视频分类示例
const classifyVideo = async (videoPath: string) => {
  const result = await invoke('start_video_classification', {
    request: {
      material_id: materialId,
      project_id: projectId,
      overwrite_existing: false
    }
  });
  return result;
};
```

### 数据库操作

```rust
// Rust后端数据库操作示例
pub async fn create_material(
    repository: &MaterialRepository,
    request: CreateMaterialRequest,
) -> Result<Material> {
    let material = Material::new(request);
    repository.create(&material).await
}
```

## 📱 功能截图

### 主界面
- 项目管理界面
- 素材导入和管理
- AI分类设置

### 核心功能
- 智能视频分析
- 模板匹配系统
- 批量处理工具

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 如何贡献

1. **Fork 项目**
2. **创建功能分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送到分支** (`git push origin feature/AmazingFeature`)
5. **创建 Pull Request**

### 贡献类型

- 🐛 **Bug修复**：修复已知问题
- ✨ **新功能**：添加新的功能特性
- 📚 **文档**：改进文档和示例
- 🎨 **UI/UX**：界面和用户体验优化
- ⚡ **性能**：性能优化和改进
- 🧪 **测试**：添加或改进测试用例

## 📋 更新日志

### v0.1.25 (最新)
- ✨ 添加项目详情页面筛选功能
- 🐛 修复无限请求后台数据问题
- 🎨 优化模特名称显示
- ⚡ 提升UI响应性能

### v0.1.24
- ✨ 实现AI分类设置管理
- 🔧 优化视频分类流程
- 📊 添加分类统计功能

查看完整更新日志：[CHANGELOG.md](CHANGELOG.md)

## 🔗 相关链接

- **项目主页**：[MixVideo官网](https://mixvideo.ai)
- **文档中心**：[开发文档](https://docs.mixvideo.ai)
- **API文档**：[API参考](https://api.mixvideo.ai)
- **社区论坛**：[讨论区](https://community.mixvideo.ai)

## 📞 联系我们

- **邮箱**：<EMAIL>
- **微信群**：扫码加入开发者群
- **QQ群**：123456789
- **GitHub Issues**：[问题反馈](https://github.com/mixvideo/mixvideo/issues)

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE) - 查看 LICENSE 文件了解详情。

## 🙏 致谢

感谢以下开源项目和技术：

- [Tauri](https://tauri.app/) - 跨平台桌面应用框架
- [React](https://reactjs.org/) - 用户界面库
- [Rust](https://www.rust-lang.org/) - 系统编程语言
- [SQLite](https://www.sqlite.org/) - 嵌入式数据库
- [FFmpeg](https://ffmpeg.org/) - 多媒体处理库
- [Google Gemini](https://ai.google.dev/) - AI分析服务

---

<div align="center">

**⭐ 如果这个项目对你有帮助，请给我们一个星标！**

Made with ❤️ by MixVideo Team

</div>

### 1.3 核心价值主张
- **创作者价值**：提供AI工具和模板资源，大幅提升创作效率和内容质量
- **消费者价值**：获得个性化、高质量的视频内容和服务
- **平台价值**：构建可持续的创作者经济生态系统

🎬 **MixVideo** - 基于 Tauri 构建的跨平台多媒体处理桌面应用

## ✨ 特性

- 🚀 **跨平台**: 支持 Windows、macOS、Linux
- ⚡ **高性能**: 基于 Rust + Tauri 2.0 构建
- 🎨 **现代界面**: React + TypeScript + Vite
- 🔧 **可扩展**: 支持多语言微服务架构
- 📦 **多包管理**: PNPM Workspace + Cargo Workspace

## 🏗️ 项目结构

```
mixvideo/
├── apps/
│   └── desktop/           # Tauri 桌面应用
├── packages/
│   ├── services/          # 微服务 (Python, Rust)
│   └── shared/            # 共享代码和协议
├── tools/                 # 开发工具
└── docs/                  # 文档
```

## 🚀 快速开始

### 环境要求

- **Node.js** 18+
- **Rust** 1.70+
- **PNPM** 8+

### 安装依赖

```bash
pnpm install
```

### 开发模式

```bash
# 启动桌面应用开发模式
pnpm tauri:dev

# 或者直接在 apps/desktop 目录下
cd apps/desktop
pnpm dev
```

### 构建应用

```bash
# 构建生产版本
pnpm tauri:build

# 或者在 apps/desktop 目录下
cd apps/desktop
pnpm tauri build
```

## 📦 发布

### 手动发布

1. 构建应用: `pnpm tauri:build`
2. 安装包位置: `target/release/bundle/`
   - Windows: `.msi` 和 `.exe` 安装包
   - macOS: `.dmg` 和 `.app` 包
   - Linux: `.deb`, `.rpm` 和 `.AppImage`

### 自动发布 (GitHub Actions)

1. 创建新的 Git 标签:
   ```bash
   git tag v0.1.0
   git push origin v0.1.0
   ```

2. GitHub Actions 将自动构建并创建 Release

## 🛠️ 技术栈

### 桌面应用
- **前端**: React 18 + TypeScript + Vite
- **后端**: Rust + Tauri 2.0
- **状态管理**: 待定
- **UI 组件**: 待定

### 微服务 (计划中)
- **Python 服务**: FastAPI + gRPC
- **Rust 服务**: Tonic + gRPC
- **通信协议**: Protocol Buffers

## 📝 开发指南

### 推荐 IDE

- **VS Code** + 扩展:
  - Tauri
  - rust-analyzer
  - ES7+ React/Redux/React-Native snippets

### 代码规范

- **Rust**: 使用 `cargo fmt` 和 `cargo clippy`
- **TypeScript**: 使用 ESLint + Prettier
- **Python**: 使用 Black + isort + flake8

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 👨‍💻 作者

**imeepos** - [GitHub](https://github.com/imeepos)