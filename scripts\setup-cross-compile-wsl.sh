#!/bin/bash

# 在 WSL2 中设置 macOS 交叉编译环境
# 注意：这需要合法的 macOS SDK

set -e

echo "🔧 设置 macOS 交叉编译环境 (WSL2)"

# 检查是否在 WSL 中运行
if ! grep -q Microsoft /proc/version; then
    echo "❌ 此脚本需要在 WSL2 中运行"
    exit 1
fi

# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要的工具
echo "📦 安装构建工具..."
sudo apt install -y \
    build-essential \
    clang \
    llvm \
    git \
    curl \
    wget \
    unzip \
    cmake \
    libssl-dev \
    pkg-config \
    python3 \
    python3-pip

# 安装 Node.js
echo "📦 安装 Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 pnpm
echo "📦 安装 pnpm..."
npm install -g pnpm

# 安装 Rust
echo "🦀 安装 Rust..."
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
source ~/.cargo/env

# 添加 macOS 目标
echo "🎯 添加 macOS 构建目标..."
rustup target add x86_64-apple-darwin
rustup target add aarch64-apple-darwin

# 克隆 OSXCross
echo "🔗 设置 OSXCross..."
cd /tmp
git clone https://github.com/tpoechtrager/osxcross.git
cd osxcross

# 注意：你需要将 MacOSX SDK 放在这里
echo "⚠️  重要提示："
echo "   你需要将合法的 MacOSX SDK 文件放在 osxcross/tarballs/ 目录中"
echo "   例如：MacOSX10.15.sdk.tar.xz"
echo "   SDK 可以从 Xcode 中提取，但需要 Apple 开发者许可"
echo ""
echo "   获取 SDK 的合法方式："
echo "   1. 在 Mac 上安装 Xcode"
echo "   2. 运行：tar -czf MacOSX.sdk.tar.gz /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk"
echo "   3. 将文件复制到此目录"
echo ""

read -p "是否已经放置了 SDK 文件？(y/N): " confirm
if [[ $confirm != [yY] ]]; then
    echo "请先获取并放置 SDK 文件，然后重新运行此脚本"
    exit 1
fi

# 构建 OSXCross
echo "🏗️ 构建 OSXCross..."
UNATTENDED=1 ./build.sh

# 设置环境变量
echo "🔧 配置环境变量..."
export PATH="/tmp/osxcross/target/bin:$PATH"

# 添加到 bashrc
echo 'export PATH="/tmp/osxcross/target/bin:$PATH"' >> ~/.bashrc
echo 'export CC_x86_64_apple_darwin=x86_64-apple-darwin20.4-clang' >> ~/.bashrc
echo 'export CXX_x86_64_apple_darwin=x86_64-apple-darwin20.4-clang++' >> ~/.bashrc
echo 'export AR_x86_64_apple_darwin=x86_64-apple-darwin20.4-ar' >> ~/.bashrc
echo 'export CC_aarch64_apple_darwin=aarch64-apple-darwin20.4-clang' >> ~/.bashrc
echo 'export CXX_aarch64_apple_darwin=aarch64-apple-darwin20.4-clang++' >> ~/.bashrc
echo 'export AR_aarch64_apple_darwin=aarch64-apple-darwin20.4-ar' >> ~/.bashrc

# 安装 Tauri CLI
echo "🔧 安装 Tauri CLI..."
cargo install tauri-cli

echo "✅ 交叉编译环境设置完成！"
echo ""
echo "现在你可以在 WSL2 中构建 macOS 应用："
echo "  cd /mnt/c/Users/<USER>/Desktop/AI"
echo "  pnpm install"
echo "  cd apps/desktop"
echo "  pnpm build"
echo "  tauri build --target x86_64-apple-darwin"
echo ""
echo "⚠️  注意：首次构建可能需要很长时间"
