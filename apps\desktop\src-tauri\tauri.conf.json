{"$schema": "https://schema.tauri.app/config/2", "productName": "MixVideo Desktop", "version": "0.2.1", "identifier": "com.mixvideo.desktop", "build": {"beforeDevCommand": "vite", "devUrl": "http://localhost:5173", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "MixVideo Desktop", "width": 1200, "height": 900, "minWidth": 1200, "minHeight": 900, "center": true, "resizable": true, "maximizable": true, "minimizable": true, "closable": true}], "security": {"csp": null, "assetProtocol": {"enable": true, "scope": ["**"]}}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "publisher": "<PERSON><PERSON><PERSON>", "copyright": "Copyright © 2024 imeepos. All rights reserved.", "category": "Productivity", "shortDescription": "MixVideo Desktop - 多媒体处理桌面应用", "longDescription": "MixVideo Desktop 是一个基于 Tauri 构建的跨平台桌面应用，提供强大的多媒体处理功能。", "externalBin": [], "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}, "macOS": {"frameworks": [], "minimumSystemVersion": "10.15", "exceptionDomain": "", "signingIdentity": null, "providerShortName": null, "entitlements": null, "dmg": {"appPosition": {"x": 180, "y": 170}, "applicationFolderPosition": {"x": 480, "y": 170}, "windowSize": {"width": 660, "height": 400}}}}}