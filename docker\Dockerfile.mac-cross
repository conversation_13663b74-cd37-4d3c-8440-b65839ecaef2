# 用于在 Linux 上交叉编译 macOS 应用的 Dockerfile
FROM ubuntu:22.04

# 安装基础工具
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    clang \
    llvm \
    libssl-dev \
    pkg-config \
    wget \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# 安装 Node.js
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# 安装 pnpm
RUN npm install -g pnpm

# 安装 Rust
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
ENV PATH="/root/.cargo/bin:${PATH}"

# 添加 macOS 目标
RUN rustup target add x86_64-apple-darwin aarch64-apple-darwin

# 下载 macOS SDK (需要从合法来源获取)
# 注意：这里需要你提供合法的 macOS SDK
# COPY MacOSX.sdk /opt/MacOSX.sdk

# 设置交叉编译环境变量
ENV CC_x86_64_apple_darwin=x86_64-apple-darwin-clang
ENV CXX_x86_64_apple_darwin=x86_64-apple-darwin-clang++
ENV AR_x86_64_apple_darwin=x86_64-apple-darwin-ar
ENV CC_aarch64_apple_darwin=aarch64-apple-darwin-clang
ENV CXX_aarch64_apple_darwin=aarch64-apple-darwin-clang++
ENV AR_aarch64_apple_darwin=aarch64-apple-darwin-ar

# 安装 Tauri CLI
RUN cargo install tauri-cli

WORKDIR /app

# 复制项目文件
COPY . .

# 安装依赖
RUN pnpm install

# 构建脚本
CMD ["bash", "-c", "cd apps/desktop && pnpm build && tauri build --target x86_64-apple-darwin"]
