import {
  Code,
  Wrench,
  Database,
  FileSearch,
  Search,
  Sparkles,
  Heart,
  ArrowLeftRight
} from 'lucide-react';
import { Tool, ToolCategory, ToolStatus } from '../types/tool';

/**
 * 工具数据配置
 * 定义所有可用的工具及其属性
 */
export const TOOLS_DATA: Tool[] = [
  {
    id: 'similarity-search',
    name: '相似度检索工具',
    description: '基于AI的智能相似度搜索工具，支持多种相关性阈值和快速搜索功能',
    longDescription: '强大的AI驱动相似度检索工具，基于先进的机器学习算法提供精准的内容匹配。支持可调节的相关性阈值、智能搜索建议、实时结果展示和批量处理功能。适用于图像、文本和多媒体内容的相似性分析。',
    icon: Search,
    route: '/tools/similarity-search',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['AI搜索', '相似度检索', '智能匹配', '机器学习', '内容分析'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-25'
  },
  {
    id: 'outfit-recommendation',
    name: 'AI穿搭方案推荐',
    description: '基于TikTok视觉趋势的智能穿搭建议工具，提供个性化的时尚搭配方案',
    longDescription: '专业的AI穿搭顾问工具，基于TikTok视觉趋势和时尚潮流，为用户生成个性化的穿搭方案。支持多种风格选择、场合匹配、色彩搭配建议，并提供TikTok优化建议和拍摄技巧，助力内容创作和时尚搭配。',
    icon: Sparkles,
    route: '/tools/outfit-recommendation',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['AI穿搭', '时尚搭配', 'TikTok', '个性化推荐', '视觉趋势'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-25'
  },
  {
    id: 'outfit-search',
    name: '智能服装搜索',
    description: '基于AI的智能服装搜索工具，支持图像解析、相似度搜索和LLM问答',
    longDescription: '专业的服装搜索工具，基于先进的AI技术提供智能服装匹配和搜索功能。支持图像上传解析、多维度过滤搜索、相似度匹配、LLM智能问答等功能。提供直观的双列布局界面，左侧展示搜索结果，右侧提供搜索控制面板。',
    icon: Search,
    route: '/tools/outfit-search',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.BETA,
    tags: ['AI搜索', '服装匹配', '图像解析', 'LLM问答', '相似度搜索'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-26'
  },
  {
    id: 'outfit-favorites',
    name: '穿搭方案收藏管理',
    description: '管理收藏的穿搭方案，支持基于收藏方案的智能素材检索',
    longDescription: '专业的穿搭方案收藏管理工具，提供完整的收藏管理功能。支持收藏方案的添加、删除、搜索和筛选，以及基于收藏方案的智能素材检索功能。帮助用户建立个人穿搭方案库，快速找到适合的素材。',
    icon: Heart,
    route: '/tools/outfit-favorites',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['收藏管理', '穿搭方案', '素材检索', '个人库', '智能搜索'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-27'
  },
  {
    id: 'outfit-comparison',
    name: '穿搭方案对比分析',
    description: '分屏对比两个收藏方案的素材检索结果，分析方案差异',
    longDescription: '强大的穿搭方案对比分析工具，支持同时选择两个收藏方案进行分屏对比。并行执行素材检索，直观展示两个方案的检索结果差异，帮助用户更好地理解不同方案的特点和适用场景。',
    icon: ArrowLeftRight,
    route: '/tools/outfit-comparison',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['方案对比', '分屏展示', '差异分析', '素材检索', '对比分析'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-27'
  },
  {
    id: 'material-search',
    name: '智能素材检索',
    description: '基于收藏穿搭方案的智能素材检索工具，快速找到匹配的素材',
    longDescription: '专业的智能素材检索工具，基于收藏的穿搭方案进行精准的素材匹配。支持多维度检索条件生成、相关度排序、分页浏览等功能。提供直观的检索界面和丰富的筛选选项，帮助用户快速找到最适合的素材。',
    icon: Search,
    route: '/tools/material-search',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['素材检索', '智能匹配', '方案关联', '相关度排序', '精准搜索'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-27'
  }
];

/**
 * 根据ID获取工具信息
 */
export const getToolById = (id: string): Tool | undefined => {
  return TOOLS_DATA.find(tool => tool.id === id);
};

/**
 * 根据分类获取工具列表
 */
export const getToolsByCategory = (category: ToolCategory): Tool[] => {
  return TOOLS_DATA.filter(tool => tool.category === category);
};

/**
 * 获取热门工具列表
 */
export const getPopularTools = (): Tool[] => {
  return TOOLS_DATA.filter(tool => tool.isPopular);
};

/**
 * 获取新功能工具列表
 */
export const getNewTools = (): Tool[] => {
  return TOOLS_DATA.filter(tool => tool.isNew);
};

/**
 * 搜索工具
 */
export const searchTools = (query: string): Tool[] => {
  const lowercaseQuery = query.toLowerCase();
  return TOOLS_DATA.filter(tool => 
    tool.name.toLowerCase().includes(lowercaseQuery) ||
    tool.description.toLowerCase().includes(lowercaseQuery) ||
    tool.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
};

/**
 * 工具分类配置
 */
export const TOOL_CATEGORIES = [
  {
    id: ToolCategory.DATA_PROCESSING,
    name: '数据处理',
    description: '数据清洗、转换和处理工具',
    icon: Database,
    color: 'purple'
  },
  {
    id: ToolCategory.DEVELOPMENT,
    name: '开发调试',
    description: '开发和调试相关工具',
    icon: Code,
    color: 'indigo'
  },
  {
    id: ToolCategory.FILE_PROCESSING,
    name: '文件处理',
    description: '文件操作和处理工具',
    icon: FileSearch,
    color: 'orange'
  },
  {
    id: ToolCategory.AI_TOOLS,
    name: 'AI工具',
    description: '人工智能相关工具',
    icon: Wrench,
    color: 'pink'
  },
  {
    id: ToolCategory.UTILITIES,
    name: '实用工具',
    description: '通用实用工具集合',
    icon: Wrench,
    color: 'teal'
  }
];

/**
 * 获取分类配置
 */
export const getCategoryConfig = (category: ToolCategory) => {
  return TOOL_CATEGORIES.find(cat => cat.id === category);
};
