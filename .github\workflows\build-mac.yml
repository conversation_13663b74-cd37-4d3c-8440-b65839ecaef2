name: Build macOS App

on:
  push:
    branches: [ main, master ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build-macos:
    runs-on: macos-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8

    - name: Setup Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        targets: aarch64-apple-darwin,x86_64-apple-darwin

    - name: Rust cache
      uses: swatinem/rust-cache@v2
      with:
        workspaces: './apps/desktop/src-tauri -> target'

    - name: Install dependencies
      run: pnpm install

    - name: Install Tauri CLI
      run: pnpm add -g @tauri-apps/cli

    - name: Build frontend
      run: |
        cd apps/desktop
        pnpm build

    - name: Build Tauri app (Universal)
      run: |
        cd apps/desktop
        pnpm tauri build --target universal-apple-darwin
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Upload macOS artifacts
      uses: actions/upload-artifact@v4
      with:
        name: macos-app
        path: |
          apps/desktop/src-tauri/target/universal-apple-darwin/release/bundle/dmg/*.dmg
          apps/desktop/src-tauri/target/universal-apple-darwin/release/bundle/macos/*.app

  build-macos-intel:
    runs-on: macos-13  # Intel runner
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8

    - name: Setup Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        targets: x86_64-apple-darwin

    - name: Rust cache
      uses: swatinem/rust-cache@v2
      with:
        workspaces: './apps/desktop/src-tauri -> target'

    - name: Install dependencies
      run: pnpm install

    - name: Install Tauri CLI
      run: pnpm add -g @tauri-apps/cli

    - name: Build frontend
      run: |
        cd apps/desktop
        pnpm build

    - name: Build Tauri app (Intel)
      run: |
        cd apps/desktop
        pnpm tauri build --target x86_64-apple-darwin
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Upload Intel artifacts
      uses: actions/upload-artifact@v4
      with:
        name: macos-intel-app
        path: |
          apps/desktop/src-tauri/target/x86_64-apple-darwin/release/bundle/dmg/*.dmg
          apps/desktop/src-tauri/target/x86_64-apple-darwin/release/bundle/macos/*.app

  build-macos-arm:
    runs-on: macos-latest  # ARM runner
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8

    - name: Setup Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        targets: aarch64-apple-darwin

    - name: Rust cache
      uses: swatinem/rust-cache@v2
      with:
        workspaces: './apps/desktop/src-tauri -> target'

    - name: Install dependencies
      run: pnpm install

    - name: Install Tauri CLI
      run: pnpm add -g @tauri-apps/cli

    - name: Build frontend
      run: |
        cd apps/desktop
        pnpm build

    - name: Build Tauri app (ARM)
      run: |
        cd apps/desktop
        pnpm tauri build --target aarch64-apple-darwin
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Upload ARM artifacts
      uses: actions/upload-artifact@v4
      with:
        name: macos-arm-app
        path: |
          apps/desktop/src-tauri/target/aarch64-apple-darwin/release/bundle/dmg/*.dmg
          apps/desktop/src-tauri/target/aarch64-apple-darwin/release/bundle/macos/*.app

  release:
    if: startsWith(github.ref, 'refs/tags/v')
    needs: [build-macos, build-macos-intel, build-macos-arm]
    runs-on: ubuntu-latest
    
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v4
      
    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          macos-app/**/*
          macos-intel-app/**/*
          macos-arm-app/**/*
        draft: true
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
