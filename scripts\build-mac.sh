#!/bin/bash

# MixVideo Mac 构建脚本
# 用于构建 macOS 版本的 MixVideo Desktop 应用

set -e

echo "🍎 开始构建 MixVideo Mac 客户端..."

# 检查是否在 macOS 系统上运行
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ 错误: 此脚本只能在 macOS 系统上运行"
    exit 1
fi

# 检查必要的工具
echo "🔍 检查构建环境..."

# 检查 Rust
if ! command -v rustc &> /dev/null; then
    echo "❌ 错误: 未找到 Rust，请先安装 Rust"
    echo "安装命令: curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh"
    exit 1
fi

# 检查 pnpm
if ! command -v pnpm &> /dev/null; then
    echo "❌ 错误: 未找到 pnpm，请先安装 pnpm"
    echo "安装命令: npm install -g pnpm"
    exit 1
fi

# 检查 Tauri CLI
if ! command -v tauri &> /dev/null; then
    echo "📦 安装 Tauri CLI..."
    pnpm add -g @tauri-apps/cli
fi

# 添加 macOS 目标平台
echo "🎯 添加 macOS 构建目标..."
rustup target add x86_64-apple-darwin
rustup target add aarch64-apple-darwin

# 进入桌面应用目录
cd apps/desktop

# 安装依赖
echo "📦 安装依赖..."
pnpm install

# 构建前端
echo "🏗️ 构建前端..."
pnpm build

# 构建类型选择
echo "请选择构建类型:"
echo "1) Universal (Intel + Apple Silicon)"
echo "2) Intel only (x86_64)"
echo "3) Apple Silicon only (ARM64)"
echo "4) Debug 版本"

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "🚀 构建 Universal macOS 应用..."
        pnpm tauri:build:mac
        ;;
    2)
        echo "🚀 构建 Intel macOS 应用..."
        pnpm tauri:build:mac:intel
        ;;
    3)
        echo "🚀 构建 Apple Silicon macOS 应用..."
        pnpm tauri:build:mac:arm
        ;;
    4)
        echo "🚀 构建 Debug 版本..."
        pnpm tauri:build:debug
        ;;
    *)
        echo "❌ 无效选择，默认构建 Universal 版本"
        pnpm tauri:build:mac
        ;;
esac

echo "✅ Mac 客户端构建完成!"
echo "📁 构建产物位置: src-tauri/target/release/bundle/"
echo "   - .app 文件: src-tauri/target/release/bundle/macos/"
echo "   - .dmg 文件: src-tauri/target/release/bundle/dmg/"

# 显示构建产物信息
if [ -d "src-tauri/target/release/bundle/macos" ]; then
    echo ""
    echo "📊 构建产物信息:"
    ls -la src-tauri/target/release/bundle/macos/
fi

if [ -d "src-tauri/target/release/bundle/dmg" ]; then
    echo ""
    echo "💿 DMG 文件:"
    ls -la src-tauri/target/release/bundle/dmg/
fi

echo ""
echo "🎉 构建完成! 您可以在 Finder 中打开构建目录查看应用程序。"
