{"name": "@mixvideo/desktop", "private": true, "version": "0.2.1", "type": "module", "scripts": {"dev": "tauri dev", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "tauri:build:mac": "tauri build --target universal-apple-darwin", "tauri:build:mac:intel": "tauri build --target x86_64-apple-darwin", "tauri:build:mac:arm": "tauri build --target aarch64-apple-darwin", "tauri:build:debug": "tauri build --debug", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:unit": "vitest run src/tests/components", "test:integration": "vitest run src/tests/integration", "test:performance": "vitest run src/tests/performance", "test:e2e": "vitest run src/tests/e2e", "test:all": "bash scripts/run-tests.sh"}, "dependencies": {"@heroicons/react": "^2.2.0", "@tauri-apps/api": "^2.6.0", "@tauri-apps/plugin-dialog": "^2", "@tauri-apps/plugin-fs": "^2", "@tauri-apps/plugin-opener": "^2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "marked": "^16.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.20.1", "react-window": "^1.8.11", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "zustand": "^4.4.7"}, "devDependencies": {"@tauri-apps/cli": "^2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.16", "jsdom": "^23.0.1", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "~5.6.2", "vite": "^6.0.3", "vitest": "^1.0.0"}}