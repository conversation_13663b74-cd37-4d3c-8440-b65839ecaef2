# MixVideo Mac 客户端设置指南

本文档介绍如何在 macOS 上设置和构建 MixVideo 桌面应用。

## 系统要求

- macOS 10.15 (Catalina) 或更高版本
- Xcode Command Line Tools
- 至少 4GB 可用磁盘空间

## 快速开始

### 自动设置（推荐）

运行自动设置脚本：

```bash
bash scripts/setup-mac.sh
```

这个脚本会自动安装所有必要的依赖和工具。

### 手动设置

如果您喜欢手动设置，请按照以下步骤：

#### 1. 安装 Homebrew

```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

#### 2. 安装 Node.js 和 pnpm

```bash
brew install node
npm install -g pnpm
```

#### 3. 安装 Rust

```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env
```

#### 4. 添加 macOS 构建目标

```bash
rustup target add x86_64-apple-darwin
rustup target add aarch64-apple-darwin
```

#### 5. 安装 Tauri CLI

```bash
pnpm add -g @tauri-apps/cli
```

#### 6. 安装项目依赖

```bash
pnpm install
```

## 开发

### 启动开发服务器

```bash
pnpm tauri:dev
```

这会启动前端开发服务器并打开 Tauri 应用窗口。

### 热重载

在开发模式下，前端代码的更改会自动重载。如果您修改了 Rust 代码，应用会自动重新编译。

## 构建

### 使用构建脚本（推荐）

```bash
bash scripts/build-mac.sh
```

这个脚本提供了交互式选项来选择构建类型。

### 手动构建

#### Universal 构建（Intel + Apple Silicon）

```bash
pnpm tauri:build:mac
```

#### Intel 专用构建

```bash
pnpm tauri:build:mac:intel
```

#### Apple Silicon 专用构建

```bash
pnpm tauri:build:mac:arm
```

#### Debug 构建

```bash
pnpm tauri:build:debug
```

## 构建产物

构建完成后，您可以在以下位置找到应用程序：

- **应用程序包**: `apps/desktop/src-tauri/target/release/bundle/macos/MixVideo Desktop.app`
- **DMG 安装包**: `apps/desktop/src-tauri/target/release/bundle/dmg/MixVideo Desktop_*.dmg`

## 代码签名和公证

### 开发签名

对于本地开发和测试，Tauri 会自动使用开发者证书进行签名。

### 发布签名

对于 App Store 或独立发布，您需要：

1. 在 Apple Developer 账户中创建应用程序标识符
2. 创建发布证书
3. 在 `tauri.conf.json` 中配置签名身份：

```json
{
  "bundle": {
    "macOS": {
      "signingIdentity": "Developer ID Application: Your Name (TEAM_ID)"
    }
  }
}
```

### 公证

对于 macOS 10.15+ 的分发，您需要对应用进行公证：

```bash
# 构建并签名
pnpm tauri:build:mac

# 上传到 Apple 进行公证
xcrun notarytool submit "path/to/app.dmg" --keychain-profile "notarytool-profile" --wait

# 装订公证票据
xcrun stapler staple "path/to/app.dmg"
```

## 故障排除

### 常见问题

#### 1. "xcrun: error: invalid active developer path"

解决方案：
```bash
xcode-select --install
```

#### 2. "error: Microsoft Visual C++ 14.0 is required"

这个错误在 macOS 上不应该出现。如果遇到，请检查您是否在正确的平台上运行。

#### 3. 构建失败：找不到目标

确保已添加所需的构建目标：
```bash
rustup target add x86_64-apple-darwin aarch64-apple-darwin
```

#### 4. 权限错误

确保脚本有执行权限：
```bash
chmod +x scripts/build-mac.sh
chmod +x scripts/setup-mac.sh
```

### 获取帮助

如果遇到问题，请：

1. 检查 [Tauri 官方文档](https://tauri.app/v1/guides/)
2. 查看项目的 GitHub Issues
3. 在项目仓库中创建新的 Issue

## 性能优化

### 构建优化

- 使用 `--release` 标志进行发布构建
- 考虑使用 `universal-apple-darwin` 目标以支持所有 Mac 设备
- 启用 LTO (Link Time Optimization) 以获得更小的二进制文件

### 运行时优化

- 确保应用程序包含必要的权限
- 优化资源文件大小
- 使用适当的图标分辨率

## 更新日志

查看 `CHANGELOG.md` 了解最新的更改和功能。
